package vip.xiaonuo.choiceway.modular.batch.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.choiceway.modular.batch.entity.MesBatch;
import vip.xiaonuo.choiceway.modular.batch.mapper.MesBatchMapper;
import vip.xiaonuo.choiceway.modular.batch.param.*;
import vip.xiaonuo.choiceway.modular.batch.service.MesBatchService;
import vip.xiaonuo.choiceway.modular.process.entity.MesProcess;
import vip.xiaonuo.choiceway.modular.process.entity.MesProcessList;
import vip.xiaonuo.choiceway.modular.process.service.MesProcessService;
import vip.xiaonuo.choiceway.modular.process.service.MesProcessListService;
import vip.xiaonuo.choiceway.modular.transaction.param.MesTransactionRecordParam;
import vip.xiaonuo.choiceway.modular.transaction.service.MesTransactionService;
import vip.xiaonuo.choiceway.modular.workorder.entity.MesWorkOrder;
import vip.xiaonuo.choiceway.modular.workorder.service.MesWorkOrderService;
import vip.xiaonuo.choiceway.modular.scrap.service.MesScrapRecordService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * MES批次Service接口实现类
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
@Service
public class MesBatchServiceImpl extends ServiceImpl<MesBatchMapper, MesBatch> implements MesBatchService {

    @Resource
    private MesWorkOrderService mesWorkOrderService;

    @Resource
    private MesScrapRecordService mesScrapRecordService;

    @Resource
    private MesTransactionService mesTransactionService;

    @Resource
    private MesProcessService mesProcessService;

    @Resource
    private MesProcessListService mesProcessListService;

    @Override
    public Page<MesBatch> page(MesBatchPageParam mesBatchPageParam) {
        QueryWrapper<MesBatch> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotEmpty(mesBatchPageParam.getLotId())) {
            queryWrapper.lambda().like(MesBatch::getLotId, mesBatchPageParam.getLotId());
        }
        if(ObjectUtil.isNotEmpty(mesBatchPageParam.getProductName())) {
            queryWrapper.lambda().like(MesBatch::getProductName, mesBatchPageParam.getProductName());
        }
        if(ObjectUtil.isNotEmpty(mesBatchPageParam.getCurrentStationId())) {
            queryWrapper.lambda().eq(MesBatch::getCurrentStationId, mesBatchPageParam.getCurrentStationId());
        }
        if(ObjectUtil.isNotEmpty(mesBatchPageParam.getStatus())) {
            queryWrapper.lambda().eq(MesBatch::getStatus, mesBatchPageParam.getStatus());
        }
        if(ObjectUtil.isNotEmpty(mesBatchPageParam.getProcessId())) {
            queryWrapper.lambda().eq(MesBatch::getProcessId, mesBatchPageParam.getProcessId());
        }
        if(ObjectUtil.isNotEmpty(mesBatchPageParam.getSearchKey())) {
            queryWrapper.lambda().like(MesBatch::getLotId, mesBatchPageParam.getSearchKey())
                    .or().like(MesBatch::getProductName, mesBatchPageParam.getSearchKey());
        }
        if(ObjectUtil.isAllNotEmpty(mesBatchPageParam.getSortField(), mesBatchPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(mesBatchPageParam.getSortOrder());
            queryWrapper.orderBy(true, mesBatchPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(mesBatchPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(MesBatch::getCreateTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(MesBatchAddParam mesBatchAddParam) {
        // 检查批次ID是否重复
        boolean exists = this.count(new LambdaQueryWrapper<MesBatch>()
                .eq(MesBatch::getLotId, mesBatchAddParam.getLotId())) > 0;
        if(exists) {
            throw new CommonException("批次ID已存在：{}", mesBatchAddParam.getLotId());
        }

        // 获取流程信息
        MesProcess process = mesProcessService.queryEntity(mesBatchAddParam.getProcessId());

        List<MesProcessList> processNodes = mesProcessListService.getNodesByProcessId(mesBatchAddParam.getProcessId());
        MesProcessList firstStation = processNodes.stream()
                .filter(node -> "start".equals(node.getNodeType()))
                .findFirst()
                .orElse(null);
        // 1、查看用户有没有现在批次所在的站点
        if (mesBatchAddParam.getCurrentStationName() == null) {
            // 如果用户没有选择批次的站点，那么就默认是流程的第一个站点
            firstStation = processNodes.stream()
                    .filter(node -> "station".equals(node.getNodeType()))
                    .min((a, b) -> Integer.compare(a.getSequenceOrder(), b.getSequenceOrder()))
                    .orElse(null);
        }else{
            // 如果用户选择站点，那么就根据用户选择的站点来设置当前站点，但是要检查用户选择的站点是否在流程中
            if (!processNodes.stream().anyMatch(node -> mesBatchAddParam.getCurrentStationName().equals(node.getNodeName()))) {
                throw new CommonException("用户选择的站点不在流程中");
            }
            firstStation = processNodes.stream()
                    .filter(node -> mesBatchAddParam.getCurrentStationName().equals(node.getNodeName()))
                    .findFirst()
                    .orElse(null);
        }




        MesBatch mesBatch = new MesBatch();
        mesBatch.setLotId(mesBatchAddParam.getLotId());
        mesBatch.setProductName(mesBatchAddParam.getProductName());
        mesBatch.setProcessId(mesBatchAddParam.getProcessId());
        mesBatch.setProcessName(process.getProcessName());
        mesBatch.setParticleCount(mesBatchAddParam.getParticleCount());
        mesBatch.setRemarks(mesBatchAddParam.getRemarks());
        mesBatch.setStatus("WAIT"); // 默认状态为等待

        // 设置当前站点为流程的第一个站点
        if(firstStation != null) {
            mesBatch.setCurrentStationId(firstStation.getNodeId());
            mesBatch.setCurrentStationName(firstStation.getNodeName());
        }

        this.save(mesBatch);

        // 记录创建事务
        MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
        transactionParam.setBatchId(mesBatch.getId());
        transactionParam.setLotId(mesBatch.getLotId()); // 添加批次ID记录
        transactionParam.setParticleCount(mesBatch.getParticleCount()); // 添加颗粒数记录
        transactionParam.setOperationType("CREATE");
        transactionParam.setOperationDesc("批次创建");
        transactionParam.setStationId(mesBatch.getCurrentStationId());
        transactionParam.setStationName(mesBatch.getCurrentStationName());
        transactionParam.setBeforeStatus("");
        transactionParam.setAfterStatus("WAIT");
        transactionParam.setRemark(String.format("创建批次：%s，产品：%s，流程：%s，颗粒数：%d",
            mesBatch.getLotId(), mesBatch.getProductName(), mesBatch.getProcessName(), mesBatch.getParticleCount()));
        mesTransactionService.recordTransaction(transactionParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(MesBatchEditParam mesBatchEditParam) {
        MesBatch mesBatch = this.queryEntity(mesBatchEditParam.getId());
        Integer beforeParticleCount = mesBatch.getParticleCount();

        // 只允许编辑颗粒数
        mesBatch.setParticleCount(mesBatchEditParam.getParticleCount());
        this.updateById(mesBatch);

        // 记录编辑事务
        MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
        transactionParam.setBatchId(mesBatch.getId());
        transactionParam.setLotId(mesBatch.getLotId()); // 添加批次ID记录
        transactionParam.setParticleCount(mesBatch.getParticleCount()); // 添加颗粒数记录（编辑后的值）
        transactionParam.setOperationType("EDIT");
        transactionParam.setOperationDesc("批次编辑");
        transactionParam.setStationId(mesBatch.getCurrentStationId());
        transactionParam.setStationName(mesBatch.getCurrentStationName());
        transactionParam.setBeforeStatus(mesBatch.getStatus());
        transactionParam.setAfterStatus(mesBatch.getStatus());
        transactionParam.setRemark(String.format("编辑批次颗粒数：从%d改为%d",
            beforeParticleCount, mesBatchEditParam.getParticleCount()));
        mesTransactionService.recordTransaction(transactionParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<MesBatchIdParam> mesBatchIdParamList) {
        List<String> ids = CollStreamUtil.toList(mesBatchIdParamList, MesBatchIdParam::getId);

        // 在删除前记录事务历史
        for(MesBatchIdParam param : mesBatchIdParamList) {
            MesBatch mesBatch = this.queryEntity(param.getId());

            // 记录删除事务
            MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
            transactionParam.setBatchId(mesBatch.getId());
            transactionParam.setLotId(mesBatch.getLotId()); // 添加批次ID记录
            transactionParam.setParticleCount(mesBatch.getParticleCount()); // 添加颗粒数记录
            transactionParam.setOperationType("DELETE");
            transactionParam.setOperationDesc("批次删除");
            transactionParam.setStationId(mesBatch.getCurrentStationId());
            transactionParam.setStationName(mesBatch.getCurrentStationName());
            transactionParam.setBeforeStatus(mesBatch.getStatus());
            transactionParam.setAfterStatus("DELETED");
            transactionParam.setRemark(String.format("删除批次：%s，颗粒数：%d", mesBatch.getLotId(), mesBatch.getParticleCount()));
            mesTransactionService.recordTransaction(transactionParam);
        }

        this.removeByIds(ids);
    }

    @Override
    public MesBatch detail(MesBatchIdParam mesBatchIdParam) {
        return this.queryEntity(mesBatchIdParam.getId());
    }

    @Override
    public MesBatch queryEntity(String id) {
        MesBatch mesBatch = this.getById(id);
        if(ObjectUtil.isEmpty(mesBatch)) {
            throw new CommonException("批次不存在，id值为：{}", id);
        }
        return mesBatch;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String feedMaterial(MesBatchFeedParam mesBatchFeedParam) {
        // 获取工单信息
        MesWorkOrder workOrder = mesWorkOrderService.queryEntity(mesBatchFeedParam.getWorkOrderId());

        // 生成批次ID
        String lotId = "LOT" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + IdUtil.randomUUID().substring(0, 4).toUpperCase();

        // 获取流程信息
        MesProcess process = mesProcessService.queryEntity(workOrder.getProcessId());

        // 获取流程的第一个站点
        List<MesProcessList> processNodes = mesProcessListService.getNodesByProcessId(workOrder.getProcessId());
        MesProcessList firstStation = processNodes.stream()
                .filter(node -> "start".equals(node.getNodeType()))
                .findFirst()
                .orElse(null);

        if(firstStation == null) {
            // 如果没有开始节点，取第一个工位节点
            firstStation = processNodes.stream()
                    .filter(node -> "station".equals(node.getNodeType()))
                    .min((a, b) -> Integer.compare(a.getSequenceOrder(), b.getSequenceOrder()))
                    .orElse(null);
        }

        // 创建批次记录
        MesBatch mesBatch = new MesBatch();
        mesBatch.setLotId(lotId);
        mesBatch.setProductName(workOrder.getProductName());
        mesBatch.setStatus("WAIT"); // 待进站状态
        mesBatch.setProcessId(workOrder.getProcessId());
        mesBatch.setProcessName(process.getProcessName());
        mesBatch.setParticleCount(0); // 默认颗粒数
        mesBatch.setRemarks(mesBatchFeedParam.getRemark());

        // 设置当前站点为流程的第一个站点
        if(firstStation != null) {
            mesBatch.setCurrentStationId(firstStation.getNodeId());
            mesBatch.setCurrentStationName(firstStation.getNodeName());
        }

        this.save(mesBatch);

        // 记录事务
        MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
        transactionParam.setBatchId(mesBatch.getId());
        transactionParam.setWorkOrderId(workOrder.getId());
        transactionParam.setOperationType("FEED");
        transactionParam.setOperationDesc("投料生成批次");
        transactionParam.setBeforeStatus("");
        transactionParam.setAfterStatus("WAIT");
        transactionParam.setRemark(mesBatchFeedParam.getRemark());
        mesTransactionService.recordTransaction(transactionParam);

        return mesBatch.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enterStation(MesBatchEnterParam mesBatchEnterParam) {
        MesBatch mesBatch = this.queryEntity(mesBatchEnterParam.getBatchId());
        
        // 检查批次状态
        if(!"WAIT".equals(mesBatch.getStatus())) {
            throw new CommonException("批次状态不是待进站，无法进站");
        }
        
        String beforeStatus = mesBatch.getStatus();
        
        // 更新批次状态
        mesBatch.setStatus("RUN");
        mesBatch.setCurrentStationId(mesBatchEnterParam.getStationId());
        mesBatch.setCurrentStationName(mesBatchEnterParam.getStationName());
        this.updateById(mesBatch);
        
        // 记录事务
        MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
        transactionParam.setBatchId(mesBatch.getId());
        transactionParam.setLotId(mesBatch.getLotId()); // 添加批次ID记录
        transactionParam.setParticleCount(mesBatch.getParticleCount()); // 添加颗粒数记录
        transactionParam.setOperationType("ENTER");
        transactionParam.setOperationDesc("批次进站");
        transactionParam.setStationId(mesBatchEnterParam.getStationId());
        transactionParam.setStationName(mesBatchEnterParam.getStationName());
        transactionParam.setBeforeStatus(beforeStatus);
        transactionParam.setAfterStatus("RUN");
        transactionParam.setRemark(mesBatchEnterParam.getRemark());
        mesTransactionService.recordTransaction(transactionParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void exitStation(MesBatchExitParam mesBatchExitParam) {
        MesBatch mesBatch = this.queryEntity(mesBatchExitParam.getBatchId());
        
        // 检查批次状态
        if(!"RUN".equals(mesBatch.getStatus())) {
            throw new CommonException("批次状态不是运行中，无法出站");
        }
        
        String beforeStatus = mesBatch.getStatus();
        String beforeStationId = mesBatch.getCurrentStationId();
        String beforeStationName = mesBatch.getCurrentStationName();

        // 1、获取流程线
        MesProcess process = mesProcessService.queryEntity(mesBatch.getProcessId());
        if(process == null || StrUtil.isEmpty(process.getProcessConfig())) {
            throw new CommonException("流程配置不存在，请联系IT");
        }

        try {
            JSONObject processConfig = JSONUtil.parseObj(process.getProcessConfig());
            JSONArray nodes = processConfig.getJSONArray("nodes");
            JSONArray edges = processConfig.getJSONArray("edges");

            if(nodes == null || edges == null) {
                throw new CommonException("流程配置格式错误，请联系IT");
            }

            // 2、根据站点查看当前站点的位置
            String currentStationId = mesBatch.getCurrentStationId();

            // 3、获取下一个站点的信息
            JSONObject nextEdge = null;
            for(int i = 0; i < edges.size(); i++) {
                JSONObject edge = edges.getJSONObject(i);
                if(currentStationId.equals(edge.getStr("source"))) {
                    nextEdge = edge;
                    break;
                }
            }

            // 4.2、如果没有下一个站点，就提示站点信息异常，请联系IT
            if(nextEdge == null) {
                throw new CommonException("当前站点没有配置下一个站点，站点信息异常，请联系IT");
            }

            String nextStationId = nextEdge.getStr("target");

            // 查找下一个站点的详细信息
            JSONObject nextStationNode = null;
            for(int i = 0; i < nodes.size(); i++) {
                JSONObject node = nodes.getJSONObject(i);
                if(nextStationId.equals(node.getStr("id"))) {
                    nextStationNode = node;
                    break;
                }
            }

            if(nextStationNode == null) {
                throw new CommonException("下一个站点节点信息不存在，请联系IT");
            }

            String nextStationType = nextStationNode.getStr("type");
            String nextStationName = nextStationNode.getStr("name");
            String newStatus;
            String operationDesc;

            // 4、如果有下一个站点，则更新当前站点为下一个站点，并且状态修改为WAIT
            // 4.3、如果下一个站点为结束，则更新当前站点为下一个站点，并且状态修改为SHIP
            if("end".equals(nextStationType)) {
                // 下一个站点是结束节点，状态改为已入库
                newStatus = "SHIP";
                operationDesc = "批次出站完成，已到达流程终点";
            } else {
                // 下一个站点是工作站点，状态改为等待
                newStatus = "WAIT";
                operationDesc = "批次出站，等待进入下一站点";
            }

            // 更新批次信息
            mesBatch.setStatus(newStatus);
            mesBatch.setCurrentStationId(nextStationId);
            mesBatch.setCurrentStationName(nextStationName);
            this.updateById(mesBatch);

            // 记录事务
            MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
            transactionParam.setBatchId(mesBatch.getId());
            transactionParam.setLotId(mesBatch.getLotId()); // 添加批次ID记录
            transactionParam.setParticleCount(mesBatch.getParticleCount()); // 添加颗粒数记录
            transactionParam.setOperationType("EXIT");
            transactionParam.setOperationDesc(operationDesc);
            transactionParam.setStationId(beforeStationId);
            transactionParam.setStationName(beforeStationName);
            transactionParam.setBeforeStatus(beforeStatus);
            transactionParam.setAfterStatus(newStatus);
            transactionParam.setRemark(mesBatchExitParam.getRemark() +
                String.format(" [从%s出站到%s]", beforeStationName, nextStationName));
            mesTransactionService.recordTransaction(transactionParam);

        } catch (Exception e) {
            if(e instanceof CommonException) {
                throw e;
            }
            throw new CommonException("解析流程配置失败，请联系IT：" + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enterStationByLotId(MesBatchEnterLotIdParam mesBatchEnterLotIdParam) {
        MesBatch mesBatch = this.queryEntity(mesBatchEnterLotIdParam.getBatchId());

        // 验证批次LOT ID是否匹配
        if(!mesBatchEnterLotIdParam.getLotId().equals(mesBatch.getLotId())) {
            throw new CommonException("批次LOT ID不匹配");
        }

        // 检查批次状态
        if(!"WAIT".equals(mesBatch.getStatus())) {
            throw new CommonException("批次状态不是待进站，无法进站");
        }

        String beforeStatus = mesBatch.getStatus();
        Integer beforeParticleCount = mesBatch.getParticleCount();
        Integer inputParticleCount = mesBatchEnterLotIdParam.getParticleCount();

        // 检查颗粒数是否发生变化，如果变化且没有提供原因，则抛出异常
        if (!beforeParticleCount.equals(inputParticleCount)) {
            if (StrUtil.isEmpty(mesBatchEnterLotIdParam.getScrapReason())) {
                throw new CommonException("颗粒数发生变化（从{}变为{}），必须提供变化原因", beforeParticleCount, inputParticleCount);
            }

            // 记录颗粒数变化
            String operatorId = StpUtil.getLoginIdAsString();
            String operatorName = StpUtil.getLoginId().toString();

            mesScrapRecordService.recordParticleCountChange(
                mesBatch.getId(),
                mesBatch.getLotId(),
                "ENTER",
                mesBatch.getCurrentStationId(),
                mesBatch.getCurrentStationName(),
                beforeParticleCount,
                inputParticleCount,
                mesBatchEnterLotIdParam.getScrapReason(),
                operatorId,
                operatorName,
                mesBatchEnterLotIdParam.getRemark()
            );
        }

        // 更新批次状态和颗粒数
        mesBatch.setStatus("RUN");
        mesBatch.setParticleCount(inputParticleCount);
        this.updateById(mesBatch);

        // 记录事务
        MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
        transactionParam.setBatchId(mesBatch.getId());
        transactionParam.setLotId(mesBatch.getLotId());
        transactionParam.setParticleCount(inputParticleCount);
        transactionParam.setOperationType("ENTER");
        transactionParam.setOperationDesc(String.format("批次进站，颗粒数从%d更新为%d", beforeParticleCount, inputParticleCount));
        transactionParam.setStationId(mesBatch.getCurrentStationId());
        transactionParam.setStationName(mesBatch.getCurrentStationName());
        transactionParam.setBeforeStatus(beforeStatus);
        transactionParam.setAfterStatus("RUN");
        transactionParam.setRemark(mesBatchEnterLotIdParam.getRemark());
        mesTransactionService.recordTransaction(transactionParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void exitStationByLotId(MesBatchExitLotIdParam mesBatchExitLotIdParam) {
        MesBatch mesBatch = this.queryEntity(mesBatchExitLotIdParam.getBatchId());

        // 验证批次LOT ID是否匹配
        if(!mesBatchExitLotIdParam.getLotId().equals(mesBatch.getLotId())) {
            throw new CommonException("批次LOT ID不匹配");
        }

        // 检查批次状态
        if(!"RUN".equals(mesBatch.getStatus())) {
            throw new CommonException("批次状态不是运行中，无法出站");
        }

        String beforeStatus = mesBatch.getStatus();
        String beforeStationId = mesBatch.getCurrentStationId();
        String beforeStationName = mesBatch.getCurrentStationName();
        Integer beforeParticleCount = mesBatch.getParticleCount();
        Integer inputParticleCount = mesBatchExitLotIdParam.getParticleCount();

        // 检查颗粒数是否发生变化，如果变化且没有提供原因，则抛出异常
        if (!beforeParticleCount.equals(inputParticleCount)) {
            if (StrUtil.isEmpty(mesBatchExitLotIdParam.getScrapReason())) {
                throw new CommonException("颗粒数发生变化（从{}变为{}），必须提供变化原因", beforeParticleCount, inputParticleCount);
            }

            // 记录颗粒数变化
            String operatorId = StpUtil.getLoginIdAsString();
            String operatorName = StpUtil.getLoginId().toString();

            mesScrapRecordService.recordParticleCountChange(
                mesBatch.getId(),
                mesBatch.getLotId(),
                "EXIT",
                beforeStationId,
                beforeStationName,
                beforeParticleCount,
                inputParticleCount,
                mesBatchExitLotIdParam.getScrapReason(),
                operatorId,
                operatorName,
                mesBatchExitLotIdParam.getRemark()
            );
        }

        // 1、获取流程线
        MesProcess process = mesProcessService.queryEntity(mesBatch.getProcessId());
        if(process == null || StrUtil.isEmpty(process.getProcessConfig())) {
            throw new CommonException("流程配置不存在，请联系IT");
        }

        try {
            JSONObject processConfig = JSONUtil.parseObj(process.getProcessConfig());
            JSONArray nodes = processConfig.getJSONArray("nodes");
            JSONArray edges = processConfig.getJSONArray("edges");

            if(nodes == null || edges == null) {
                throw new CommonException("流程配置格式错误，请联系IT");
            }

            // 2、根据站点查看当前站点的位置
            String currentStationId = mesBatch.getCurrentStationId();

            // 3、获取下一个站点的信息
            JSONObject nextEdge = null;
            for(int i = 0; i < edges.size(); i++) {
                JSONObject edge = edges.getJSONObject(i);
                if(currentStationId.equals(edge.getStr("source"))) {
                    nextEdge = edge;
                    break;
                }
            }

            // 4.2、如果没有下一个站点，就提示站点信息异常，请联系IT
            if(nextEdge == null) {
                throw new CommonException("当前站点没有配置下一个站点，站点信息异常，请联系IT");
            }

            String nextNodeId = nextEdge.getStr("target");
            JSONObject nextNode = null;
            for(int i = 0; i < nodes.size(); i++) {
                JSONObject node = nodes.getJSONObject(i);
                if(nextNodeId.equals(node.getStr("id"))) {
                    nextNode = node;
                    break;
                }
            }

            if(nextNode == null) {
                throw new CommonException("下一个节点不存在，流程配置异常，请联系IT");
            }

            String nextNodeType = nextNode.getStr("type");
            String nextStationName = nextNode.getStr("name");
            String newStatus;
            String operationDesc;

            if("end".equals(nextNodeType)) {
                // 如果下一个节点是结束节点，批次状态改为等待
                newStatus = "WAIT";
                operationDesc = String.format("批次从%s出站，流程结束，颗粒数从%d更新为%d", beforeStationName, beforeParticleCount, inputParticleCount);
                // 保持当前站点不变
            } else if("station".equals(nextNodeType)) {
                // 如果下一个节点是工作站点，批次状态改为等待，并更新当前站点
                newStatus = "WAIT";
                operationDesc = String.format("批次从%s出站到%s，颗粒数从%d更新为%d", beforeStationName, nextStationName, beforeParticleCount, inputParticleCount);
                mesBatch.setCurrentStationId(nextNodeId);
                mesBatch.setCurrentStationName(nextStationName);
            } else {
                throw new CommonException("下一个节点类型不支持：" + nextNodeType);
            }

            // 更新批次状态和颗粒数
            mesBatch.setStatus(newStatus);
            mesBatch.setParticleCount(inputParticleCount);
            this.updateById(mesBatch);

            // 记录事务
            MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
            transactionParam.setBatchId(mesBatch.getId());
            transactionParam.setLotId(mesBatch.getLotId());
            transactionParam.setParticleCount(inputParticleCount);
            transactionParam.setOperationType("EXIT");
            transactionParam.setOperationDesc(operationDesc);
            transactionParam.setStationId(beforeStationId);
            transactionParam.setStationName(beforeStationName);
            transactionParam.setBeforeStatus(beforeStatus);
            transactionParam.setAfterStatus(newStatus);
            transactionParam.setRemark(mesBatchExitLotIdParam.getRemark() +
                String.format(" [从%s出站到%s]", beforeStationName, nextStationName));
            mesTransactionService.recordTransaction(transactionParam);

        } catch (Exception e) {
            if(e instanceof CommonException) {
                throw e;
            }
            throw new CommonException("解析流程配置失败，请联系IT：" + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void warehouse(MesBatchWarehouseParam mesBatchWarehouseParam) {
        List<MesBatch> batches = this.listByIds(mesBatchWarehouseParam.getBatchIds());
        
        if(batches.isEmpty()) {
            throw new CommonException("未找到有效的批次记录");
        }
        
        Date warehouseTime = new Date();
        
        for(MesBatch batch : batches) {
            String beforeStatus = batch.getStatus();
            
            // 更新批次状态为已入库
            batch.setStatus("SHIP");
            this.updateById(batch);

            // 记录事务
            MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
            transactionParam.setBatchId(batch.getId());
            transactionParam.setLotId(batch.getLotId()); // 添加批次ID记录
            transactionParam.setParticleCount(batch.getParticleCount()); // 添加颗粒数记录
            transactionParam.setOperationType("WAREHOUSE");
            transactionParam.setOperationDesc("批次入库");
            transactionParam.setBeforeStatus(beforeStatus);
            transactionParam.setAfterStatus("SHIP");
            transactionParam.setRemark(mesBatchWarehouseParam.getRemark());
            mesTransactionService.recordTransaction(transactionParam);
        }
    }

    @Override
    public MesBatch getBatchById(String batchId) {
        return this.getById(batchId);
    }

    @Override
    public List<MesBatch> getBatchListByIds(List<String> batchIds) {
        return this.listByIds(batchIds);
    }


    @Override
    public List<MesBatch> getWaitingBatchesByStation(String stationId) {
        return this.list(new LambdaQueryWrapper<MesBatch>()
                .eq(MesBatch::getCurrentStationId, stationId)
                .eq(MesBatch::getStatus, "WAIT")
                .orderByAsc(MesBatch::getCreateTime));
    }

    @Override
    public List<MesBatch> getRunningBatchesByStation(String stationId) {
        return this.list(new LambdaQueryWrapper<MesBatch>()
                .eq(MesBatch::getCurrentStationId, stationId)
                .eq(MesBatch::getStatus, "RUN")
                .orderByAsc(MesBatch::getCreateTime));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void regression(MesBatchRegressionParam mesBatchRegressionParam) {
        System.out.println("=== 批次退步操作开始 ===");
        System.out.println("接收到的退步参数: " + mesBatchRegressionParam);
        System.out.println("批次LOT ID列表: " + mesBatchRegressionParam.getLOT_IDS());
        System.out.println("退步备注: " + mesBatchRegressionParam.getREMARKS());

        List<String> lotIds = mesBatchRegressionParam.getLOT_IDS();
        String remarks = mesBatchRegressionParam.getREMARKS();

        if (lotIds == null || lotIds.isEmpty()) {
            System.out.println("批次LOT ID列表为空，抛出异常");
            throw new CommonException("批次LOT ID列表不能为空");
        }

        System.out.println("开始处理 " + lotIds.size() + " 个批次的退步操作");

        for (String lotId : lotIds) {
            System.out.println("--- 处理批次: " + lotId + " ---");

            // 1. 根据LOT ID查找批次
            MesBatch batch = this.getOne(new LambdaQueryWrapper<MesBatch>()
                    .eq(MesBatch::getLotId, lotId));

            if (batch == null) {
                System.out.println("批次不存在: " + lotId);
                throw new CommonException("批次不存在: " + lotId);
            }

            System.out.println("找到批次: " + batch.getId() + ", 当前状态: " + batch.getStatus() +
                             ", 当前站点: " + batch.getCurrentStationName() + "(" + batch.getCurrentStationId() + ")");

            // 2. 判断批次状态是否不为SHIP（入库状态）
            if ("SHIP".equals(batch.getStatus())) {
                System.out.println("批次已入库，无法退步: " + lotId);
                throw new CommonException("批次 " + lotId + " 已入库，无法退步");
            }

            // 3. 获取流程信息和站点列表
            MesProcess process = mesProcessService.queryEntity(batch.getProcessId());
            System.out.println("获取流程信息: " + process.getProcessName() + "(" + process.getId() + ")");

            List<MesProcessList> processNodes = mesProcessListService.getNodesByProcessId(batch.getProcessId());
            System.out.println("流程站点数量: " + processNodes.size());

            // 打印所有站点信息
            for (MesProcessList node : processNodes) {
                System.out.println("站点: " + node.getNodeName() + "(" + node.getNodeId() + "), 顺序: " + node.getSequenceOrder() + ", 类型: " + node.getNodeType());
            }

            // 4. 获取当前站点在流程中的位置
            MesProcessList currentStation = processNodes.stream()
                    .filter(node -> batch.getCurrentStationId().equals(node.getNodeId()))
                    .findFirst()
                    .orElse(null);

            if (currentStation == null) {
                System.out.println("当前站点在流程中不存在: " + batch.getCurrentStationId());
                throw new CommonException("批次 " + lotId + " 当前站点在流程中不存在，请联系IT");
            }

            System.out.println("当前站点信息: " + currentStation.getNodeName() + ", 顺序: " + currentStation.getSequenceOrder());

            // 5. 判断是否为第一个站点
            // 获取第一个工位站点
            MesProcessList firstStation = processNodes.stream()
                    .filter(node -> "station".equals(node.getNodeType()))
                    .min((a, b) -> Integer.compare(a.getSequenceOrder(), b.getSequenceOrder()))
                    .orElse(null);

            if (firstStation != null) {
                System.out.println("第一个工位站点: " + firstStation.getNodeName() + ", 顺序: " + firstStation.getSequenceOrder());

                if (currentStation.getSequenceOrder().equals(firstStation.getSequenceOrder())) {
                    System.out.println("当前站点为起始站点，无法退步: " + lotId);
                    throw new CommonException("批次 " + lotId + " 当前站点为起始站点，无法退步");
                }
            }

            // 6. 获取上一个站点
            MesProcessList previousStation = processNodes.stream()
                    .filter(node -> "station".equals(node.getNodeType()))
                    .filter(node -> node.getSequenceOrder() < currentStation.getSequenceOrder())
                    .max((a, b) -> Integer.compare(a.getSequenceOrder(), b.getSequenceOrder()))
                    .orElse(null);

            if (previousStation == null) {
                System.out.println("找不到上一个站点: " + lotId);
                throw new CommonException("批次 " + lotId + " 找不到上一个站点，无法退步");
            }

            System.out.println("上一个站点: " + previousStation.getNodeName() + "(" + previousStation.getNodeId() + "), 顺序: " + previousStation.getSequenceOrder());

            // 记录操作前的状态
            String beforeStatus = batch.getStatus();
            String beforeStationId = batch.getCurrentStationId();
            String beforeStationName = batch.getCurrentStationName();

            System.out.println("操作前状态 - 状态: " + beforeStatus + ", 站点: " + beforeStationName + "(" + beforeStationId + ")");

            // 7. 修改批次站点为上一个站点，并且修改批次状态为WAIT（待进站状态）
            batch.setCurrentStationId(previousStation.getNodeId());
            batch.setCurrentStationName(previousStation.getNodeName());
            batch.setStatus("WAIT");

            System.out.println("更新批次信息 - 新状态: WAIT, 新站点: " + previousStation.getNodeName() + "(" + previousStation.getNodeId() + ")");

            this.updateById(batch);
            System.out.println("批次信息更新完成");

            // 8. 添加退步批次历史记录
            MesTransactionRecordParam transactionParam = new MesTransactionRecordParam();
            transactionParam.setBatchId(batch.getId());
            transactionParam.setLotId(batch.getLotId());
            transactionParam.setParticleCount(batch.getParticleCount());
            transactionParam.setOperationType("REGRESSION");
            transactionParam.setOperationDesc("批次退步：从 " + beforeStationName + " 退步到 " + previousStation.getNodeName());
            transactionParam.setStationId(previousStation.getNodeId());
            transactionParam.setStationName(previousStation.getNodeName());
            transactionParam.setBeforeStatus(beforeStatus);
            transactionParam.setAfterStatus("WAIT");
            transactionParam.setRemark(remarks);

            System.out.println("记录事务历史: " + transactionParam.getOperationDesc());
            mesTransactionService.recordTransaction(transactionParam);
            System.out.println("事务历史记录完成");

            System.out.println("--- 批次 " + lotId + " 退步操作完成 ---");
        }

        System.out.println("=== 所有批次退步操作完成 ===");
    }
}
