import request from '@/utils/request'

/**
 * MES批次Api接口管理器
 *
 * <AUTHOR>
 * @date 2025/07/24
 **/

// 获取批次分页
export function batchPage(data) {
	return request({
		url: '/choiceway/batch/page',
		method: 'get',
		data: data
	})
}

// 根据批次LOT ID搜索批次（用于手机端过站页面）
export function searchBatchByLotId(lotId) {
	return request({
		url: '/choiceway/batch/page',
		method: 'get',
		data: {
			current: 1,
			size: 10,
			lotId: lotId
		}
	})
}

// 提交批次表单 add为false时为编辑，默认为新增
export function submitForm(data, add = true) {
	return request({
		url: '/choiceway/batch/' + (add ? 'add' : 'edit'),
		method: 'post',
		data: data
	})
}

// 删除批次
export function batchDelete(data) {
	return request({
		url: '/choiceway/batch/delete',
		method: 'post',
		data: data
	})
}

// 投料生成批次
export function batchFeed(data) {
	return request({
		url: '/choiceway/batch/feed',
		method: 'post',
		data: data
	})
}

// 批次进站
export function batchEnter(data) {
	return request({
		url: '/choiceway/batch/enter',
		method: 'post',
		data: data
	})
}

// 批次出站
export function batchExit(data) {
	return request({
		url: '/choiceway/batch/exit',
		method: 'post',
		data: data
	})
}

// 批次进站（根据LotId）
export function batchEnterByLotId(data) {
	return request({
		url: '/choiceway/batch/enterLotID',
		method: 'post',
		data: data
	})
}

// 批次出站（根据LotId）
export function batchExitByLotId(data) {
	return request({
		url: '/choiceway/batch/exitLotID',
		method: 'post',
		data: data
	})
}

// 批次入库
export function batchWarehouse(data) {
	return request({
		url: '/choiceway/batch/warehouse',
		method: 'post',
		data: data
	})
}

// 根据批次ID获取批次信息
export function getBatchById(batchId) {
	return request({
		url: '/choiceway/batch/getBatchById',
		method: 'get',
		data: { batchId }
	})
}

// 根据批次ID列表获取批次信息
export function getBatchListByIds(data) {
	return request({
		url: '/choiceway/batch/getBatchListByIds',
		method: 'post',
		data: data
	})
}

// 获取批次详情
export function batchDetail(data) {
	return request({
		url: '/choiceway/batch/detail',
		method: 'get',
		data: data
	})
}

// 获取当前站点的待进站批次
export function getWaitingBatches(stationId) {
	return request({
		url: '/choiceway/batch/waitingBatches',
		method: 'get',
		data: { stationId }
	})
}

// 获取当前站点的运行中批次
export function getRunningBatches(stationId) {
	return request({
		url: '/choiceway/batch/runningBatches',
		method: 'get',
		data: { stationId }
	})
}

// 批次退步
export function batchRegression(data) {
	return request({
		url: '/choiceway/batch/remarks',
		method: 'post',
		data: data
	})
}
