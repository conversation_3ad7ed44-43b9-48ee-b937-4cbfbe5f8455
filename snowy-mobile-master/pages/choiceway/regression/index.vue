<!--批次退步-->
<template>
    <view class="warehouse-container">
        <!-- 搜索区域 -->
        <view class="search-section">
<!--            <view class="search-title">批次退步</view>-->

            <!-- 输入框区域 -->
            <view class="input-wrapper">
                <uv-icon class="icon" name="scan" size="20" color="#999"></uv-icon>
                <input
                    v-model="batchId"
                    class="input"
                    type="text"
                    placeholder="请输入批次ID"
                    maxlength="50"
                    @confirm="addBatchId"
                />
                <view class="scan-btn" @tap="scanCode">
                    <uv-icon name="scan" size="20" color="#5677fc"></uv-icon>
                </view>
            </view>

            <!-- 按钮区域 -->
            <view class="action-buttons-top">
                <uv-button
                    type="default"
                    @tap="clearAll"
                    :disabled="searching || operating"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500; background: linear-gradient(135deg, #6c757d, #495057); color: #fff; border: none;"
                >
                    清空
                </uv-button>

                <uv-button
                    type="primary"
                    @tap="addBatchId"
                    :disabled="!batchId.trim() || searching"
                    :loading="searching"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500;"
                >
                    {{ searching ? '查询中' : '查询' }}
                </uv-button>

                <uv-button
                    type="warning"
                    @tap="handleWarehouseClick"
                    :disabled="operating || selectedBatches.length === 0"
                    customStyle="width: 90px; height: 45px; border-radius: 25rpx; font-size: 28rpx; font-weight: 500; background: linear-gradient(135deg, #ff6b6b, #ee5a24); border: none;"
                >
                    入库 {{ selectedBatches.length > 0 ? `(${selectedBatches.length})` : '' }}
                </uv-button>
            </view>
        </view>
        
        <!-- 批次列表 -->
        <view class="batch-list" v-if="batchList.length > 0">
            <view class="list-title">
                <text>查询结果 ({{ batchList.length }}个批次)</text>
                <uv-button
                    type="success"
                    size="mini"
                    @tap="selectAll"
                    v-if="!allSelected"
                >
                    全选
                </uv-button>
                <uv-button
                    type="default"
                    size="mini"
                    @tap="unselectAll"
                    v-else
                >
                    取消全选
                </uv-button>
            </view>

            <uv-checkbox-group v-model="selectedBatchIds" @change="onCheckboxGroupChange">
                <slide-delete
                    v-for="(item, index) in batchList"
                    :key="item.id || item.lotId"
                    :index="index"
                    :data="item"
                    @delete="handleSlideDelete"
                    style="width: 100%"
                >
                    <view
                        class="batch-item"
                        :class="{ 'selected': selectedBatchIds.includes(item.id || item.lotId), 'disabled': item.status === 'SHIP' }"
                    >
                        <view class="item-header">
                            <view class="batch-id" @tap="toggleSelectById(item.id || item.lotId)">{{ item.lotId }}</view>
                            <uv-checkbox
                                :name="item.id || item.lotId"
                                :disabled="item.status === 'SHIP'"
                                shape="circle"
                                @tap.stop
                            ></uv-checkbox>
                        </view>

                    <view class="item-content" @tap="toggleSelectById(item.id || item.lotId)">
                        <uv-row customStyle="margin-bottom: 10rpx">
                            <uv-col :span="6">
                                <text class="label">产品名称:</text>
                                <text class="value">{{ item.productName }}</text>
                            </uv-col>
                            <uv-col :span="6" textAlign="right">
                                <uv-tags
                                    :text="getStatusText(item.status)"
                                    :type="getStatusType(item.status)"
                                    size="mini"
                                ></uv-tags>
                            </uv-col>
                        </uv-row>

                        <uv-row customStyle="margin-bottom: 10rpx">
                            <uv-col :span="6">
                                <text class="label">当前站点:</text>
                                <text class="value">{{ item.currentStationName || '未设置' }}</text>
                            </uv-col>
                            <uv-col :span="6" textAlign="right">
                                <text class="label">颗粒数:</text>
                                <text class="value">{{ item.particleCount || 0 }}</text>
                            </uv-col>
                        </uv-row>
                    </view>

                    <view class="item-status" v-if="item.status === 'SHIP'">
                        <text class="status-text">该批次已入库</text>
                    </view>
                </view>
                </slide-delete>
            </uv-checkbox-group>
        </view>

        
        <!-- 空状态 -->
        <uv-empty 
            v-if="batchList.length === 0 && !searching" 
            text="请输入批次ID进行查询"
            icon="search"
        ></uv-empty>
        


        <!-- 原来的uv-modal作为备用 -->
        <uv-modal
            v-model="showWarehouseModal"
            title="确认入库"
            @confirm="confirmWarehouse"
            @cancel="handleModalCancel"
            showCancelButton
            asyncClose
            confirmText="确认入库"
            cancelText="取消"
        >
            <view class="modal-content">
                <view class="confirm-text">
                    确认将以下 {{ selectedBatches.length }} 个批次进行入库操作？
                </view>
                
                <view class="batch-summary">
                    <view 
                        class="summary-item" 
                        v-for="(batch, index) in selectedBatches" 
                        :key="index"
                    >
                        <text class="summary-id">{{ batch.lotId }}</text>
                        <text class="summary-product">{{ batch.productName }}</text>
                    </view>
                </view>
            </view>
        </uv-modal>
    </view>
</template>

<script>
import { searchBatchByLotId, batchWarehouse } from '@/api/choiceway/mesBatchApi.js'
import SlideDelete from '@/components/slide-delete/index.vue'

export default {
    components: {
        SlideDelete
    },
    data() {
        return {
            batchId: '',
            batchList: [],
            selectedBatchIds: [], // 选中的批次ID数组
            searching: false,
            operating: false,
            showWarehouseModal: false,
            warehouseRemark: ''
        }
    },
    
    computed: {
        // 已选择的批次
        selectedBatches() {
            const result = this.batchList.filter(item => {
                const batchId = item.id || item.lotId
                const isSelected = this.selectedBatchIds.includes(batchId)
                const isNotShipped = item.status !== 'SHIP'

                console.log(`批次 ${item.lotId}: ID=${batchId}, 选中=${isSelected}, 未入库=${isNotShipped}`)

                return isSelected && isNotShipped
            })

            console.log('计算的selectedBatches结果:', result)
            return result
        },

        // 是否全选
        allSelected() {
            const availableBatches = this.batchList.filter(item => item.status !== 'SHIP')
            const result = availableBatches.length > 0 &&
                   availableBatches.every(item => this.selectedBatchIds.includes(item.id || item.lotId))

            console.log('allSelected计算:', {
                availableBatches: availableBatches.length,
                selectedBatchIds: this.selectedBatchIds,
                result: result
            })

            return result
        }
    },
    
    onLoad(options) {
        if (options.batchId) {
            this.batchId = options.batchId
            this.addBatchId()
        }
    },
    
    methods: {
        // 添加批次ID
        async addBatchId() {
            if (!this.batchId.trim()) {
                uni.showToast({
                    title: '请输入批次ID',
                    icon: 'none'
                })
                return
            }

            const trimmedLotId = this.batchId.trim()

            // 检查是否已存在
            const existingBatch = this.batchList.find(item => item.lotId === trimmedLotId)
            if (existingBatch) {
                uni.showToast({
                    title: '该批次已在列表中',
                    icon: 'none'
                })
                this.batchId = ''
                return
            }

            this.searching = true

            try {
                const res = await searchBatchByLotId(trimmedLotId)

                if (res.code === 200 && res.data && res.data.records && res.data.records.length > 0) {
                    // 取第一个匹配的批次（通常lotId是唯一的）
                    const batchData = res.data.records[0]
                    console.log('查询到的批次数据:', batchData)

                    const newBatch = {
                        ...batchData,
                        selected: false // 默认不选中，用户需要手动选择
                    }

                    console.log('准备添加的批次:', newBatch)
                    console.log('批次ID字段:', newBatch.id, '批次LOT ID:', newBatch.lotId)

                    this.batchList.push(newBatch)
                    this.batchId = '' // 清空输入框

                    console.log('添加后的批次列表:', this.batchList)
                    console.log('当前selectedBatchIds:', this.selectedBatchIds)

                    uni.showToast({
                        title: '批次添加成功',
                        icon: 'success'
                    })
                } else {
                    console.log('查询失败，响应数据:', res)
                    uni.showToast({
                        title: '未找到该批次',
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('查询批次失败:', error)
                uni.showToast({
                    title: '查询失败',
                    icon: 'none'
                })
            } finally {
                this.searching = false
            }
        },

        // 扫码功能
        scanCode() {
            uni.scanCode({
                success: (res) => {
                    this.batchId = res.result
                    // 扫码成功后自动添加
                    this.addBatchId()
                },
                fail: (err) => {
                    console.error('扫码失败:', err)
                    uni.showToast({
                        title: '扫码失败',
                        icon: 'none'
                    })
                }
            })
        },
        
        // 清空所有
        clearAll() {
            this.batchId = ''
            this.batchList = []
            this.selectedBatchIds = []
            this.warehouseRemark = ''
            uni.showToast({
                title: '已清空',
                icon: 'success'
            })
        },

        // 复选框组变化
        onCheckboxGroupChange(value) {
            console.log('=== 复选框组状态变化 ===')
            console.log('新选中的ID列表:', value)
            console.log('当前批次列表:', this.batchList)
            this.selectedBatchIds = value
            console.log('更新后的selectedBatchIds:', this.selectedBatchIds)
            console.log('计算的selectedBatches:', this.selectedBatches)
            console.log('=== 复选框组状态变化结束 ===')
        },

        // 处理入库按钮点击
        handleWarehouseClick() {
            console.log('=== 入库按钮点击 ===')
            console.log('当前operating状态:', this.operating)
            console.log('selectedBatches长度:', this.selectedBatches.length)
            console.log('selectedBatches内容:', this.selectedBatches)
            console.log('selectedBatchIds:', this.selectedBatchIds)
            console.log('batchList:', this.batchList)

            if (this.operating) {
                console.log('操作进行中，忽略点击')
                return
            }

            if (this.selectedBatches.length === 0) {
                console.log('没有选中的批次')
                uni.showToast({
                    title: '请选择要入库的批次',
                    icon: 'none'
                })
                return
            }

            console.log('准备显示入库确认弹窗')

            // 使用原生弹窗作为备选方案
            const batchNames = this.selectedBatches.map(batch => batch.lotId).join(', ')
            uni.showModal({
                title: '确认入库',
                content: `确认将以下 ${this.selectedBatches.length} 个批次进行入库操作？\n\n${batchNames}`,
                showCancel: true,
                confirmText: '确认入库',
                cancelText: '取消',
                success: (res) => {
                    console.log('原生弹窗响应:', res)
                    if (res.confirm) {
                        console.log('用户确认入库')
                        this.confirmWarehouse()
                    } else {
                        console.log('用户取消入库')
                    }
                }
            })

            // 同时尝试显示自定义弹窗
            this.showWarehouseModal = true
            console.log('showWarehouseModal设置为:', this.showWarehouseModal)

            // 使用 nextTick 确保数据更新
            this.$nextTick(() => {
                console.log('nextTick后的showWarehouseModal:', this.showWarehouseModal)
            })

            console.log('=== 入库按钮点击结束 ===')
        },

        // 处理弹窗取消
        handleModalCancel() {
            console.log('=== 弹窗取消 ===')
            this.showWarehouseModal = false
            console.log('showWarehouseModal设置为:', this.showWarehouseModal)
        },

        // 根据ID切换选择状态
        toggleSelectById(batchId) {
            console.log('=== 切换选择状态 ===')
            console.log('点击的批次ID:', batchId)
            console.log('当前selectedBatchIds:', this.selectedBatchIds)

            const batch = this.batchList.find(item => (item.id || item.lotId) === batchId)
            console.log('找到的批次:', batch)

            if (batch && batch.status !== 'SHIP') {
                const index = this.selectedBatchIds.indexOf(batchId)
                console.log('在selectedBatchIds中的索引:', index)

                if (index > -1) {
                    this.selectedBatchIds.splice(index, 1)
                    console.log('从选中列表中移除:', batchId)
                } else {
                    this.selectedBatchIds.push(batchId)
                    console.log('添加到选中列表:', batchId)
                }

                console.log('更新后的selectedBatchIds:', this.selectedBatchIds)
                console.log('计算的selectedBatches:', this.selectedBatches)

                // 手动触发复选框组更新
                this.$nextTick(() => {
                    console.log('nextTick后的selectedBatchIds:', this.selectedBatchIds)
                })
            } else {
                console.log('批次不存在或已入库，无法切换状态')
            }
            console.log('=== 切换选择状态结束 ===')
        },



        // 处理左滑删除
        handleSlideDelete(data) {
            const { index, data: item } = data
            uni.showModal({
                title: '确认删除',
                content: `确定要从列表中移除批次 ${item.lotId} 吗？`,
                success: (res) => {
                    if (res.confirm) {
                        // 从选中列表中移除
                        const batchId = item.id || item.lotId
                        const selectedIndex = this.selectedBatchIds.indexOf(batchId)
                        if (selectedIndex > -1) {
                            this.selectedBatchIds.splice(selectedIndex, 1)
                        }

                        // 从批次列表中移除
                        this.batchList.splice(index, 1)

                        uni.showToast({
                            title: '已移除',
                            icon: 'success'
                        })
                    }
                }
            })
        },
        

        
        // 全选
        selectAll() {
            const availableBatchIds = this.batchList
                .filter(item => item.status !== 'SHIP')
                .map(item => item.id || item.lotId)
            this.selectedBatchIds = [...availableBatchIds]
        },

        // 取消全选
        unselectAll() {
            this.selectedBatchIds = []
        },
        
        // 确认入库
        async confirmWarehouse() {
            console.log('=== 确认入库开始 ===')
            console.log('selectedBatches:', this.selectedBatches)
            console.log('selectedBatches长度:', this.selectedBatches.length)

            if (this.selectedBatches.length === 0) {
                console.log('没有选中的批次，显示提示')
                uni.showToast({
                    title: '请选择要入库的批次',
                    icon: 'none'
                })
                return
            }

            console.log('开始入库操作')
            this.operating = true

            try {
                const batchIds = this.selectedBatches.map(item => item.id)
                console.log('提取的批次ID列表:', batchIds)

                const requestData = {
                    batchIds: batchIds,
                    remark: this.warehouseRemark
                }
                console.log('入库请求数据:', requestData)

                const res = await batchWarehouse(requestData)
                console.log('入库响应:', res)

                if (res.code === 200) {
                    console.log('入库成功')
                    uni.showToast({
                        title: '入库成功',
                        icon: 'success'
                    })

                    this.showWarehouseModal = false
                    this.warehouseRemark = ''

                    // 更新批次状态为已入库
                    console.log('更新批次状态为已入库')
                    this.selectedBatches.forEach(batch => {
                        console.log(`更新批次 ${batch.lotId} 状态为 SHIP`)
                        batch.status = 'SHIP'
                    })

                    // 清空选中状态
                    console.log('清空选中状态')
                    this.selectedBatchIds = []
                    console.log('清空后的selectedBatchIds:', this.selectedBatchIds)
                } else {
                    console.log('入库失败:', res.message)
                    uni.showToast({
                        title: res.message || '入库失败',
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('入库异常:', error)
                uni.showToast({
                    title: '入库失败',
                    icon: 'none'
                })
            } finally {
                console.log('入库操作结束，设置operating为false')
                this.operating = false
                console.log('=== 确认入库结束 ===')
            }
        },
        
        // 获取状态文本
        getStatusText(status) {
            const statusMap = {
                'WAIT': '等待',
                'RUN': '运行中',
                'SHIP': '已入库'
            }
            return statusMap[status] || status
        },
        
        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'WAIT': 'warning',
                'RUN': 'success',
                'SHIP': 'info'
            }
            return typeMap[status] || 'default'
        }
    }
}
</script>

<style scoped>
.warehouse-container {
    padding: 20rpx;
    background: #f5f5f5;
    min-height: 100vh;
}

.search-section {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
}

.search-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
}

.input-wrapper {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 0 20rpx;
    margin-bottom: 20rpx;
    height: 80rpx;
}

.input-wrapper .icon {
    margin-right: 15rpx;
}

.input-wrapper .input {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
}

.input-wrapper .input::placeholder {
    color: #999;
}

.scan-btn {
    padding: 10rpx;
    margin-left: 15rpx;
    border-radius: 6rpx;
    background: #f0f4ff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-buttons-top {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 20rpx;
    padding: 0 20rpx;
}

.action-buttons-top .uv-button {
    width: 90px !important;
    height: 45px !important;
    border-radius: 25rpx !important;
    font-size: 28rpx !important;
    font-weight: 500 !important;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.action-buttons-top .uv-button:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

/* 清空按钮样式 */
.action-buttons-top .uv-button[type="default"] {
    background: linear-gradient(135deg, #6c757d, #495057) !important;
    border: none !important;
    color: #fff !important;
}

.action-buttons-top .uv-button[type="default"]:not([disabled]):hover {
    background: linear-gradient(135deg, #495057, #343a40) !important;
    color: #fff !important;
}

/* 查询按钮样式 */
.action-buttons-top .uv-button[type="primary"] {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    border: none !important;
    color: #fff !important;
}

.action-buttons-top .uv-button[type="primary"]:not([disabled]):hover {
    background: linear-gradient(135deg, #0056b3, #004085) !important;
}

/* 入库按钮样式 */
.action-buttons-top .uv-button[type="warning"] {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
    border: none !important;
    color: #fff !important;
}

.action-buttons-top .uv-button[type="warning"]:not([disabled]):hover {
    background: linear-gradient(135deg, #ee5a24, #d63031) !important;
}

/* 禁用状态 */
.action-buttons-top .uv-button[disabled] {
    opacity: 0.6 !important;
    transform: none !important;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05) !important;
    cursor: not-allowed !important;
}

/* 加载状态动画 */
.action-buttons-top .uv-button[loading] {
    position: relative;
}

.action-buttons-top .uv-button[loading]::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20rpx;
    height: 20rpx;
    margin: -10rpx 0 0 -10rpx;
    border: 2rpx solid transparent;
    border-top: 2rpx solid #fff;
    border-radius: 50%;
    animation: button-loading 1s linear infinite;
}

@keyframes button-loading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 按钮文字渐变效果 */
.action-buttons-top .uv-button[type="warning"] {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #d63031 100%) !important;
    background-size: 200% 200% !important;
    animation: gradient-shift 3s ease infinite !important;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 入库按钮计数器样式 */
.action-buttons-top .uv-button[type="warning"] .count {
    display: inline-block;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 12rpx;
    padding: 2rpx 8rpx;
    margin-left: 8rpx;
    font-size: 24rpx;
    font-weight: bold;
}

.batch-list {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
}

.list-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

.batch-item {
    border: 2rpx solid #e4e7ed;
    border-radius: 8rpx;
    padding: 20rpx;
    transition: all 0.3s;
}

.batch-item.selected {
    border-color: #5677fc;
    background: #f0f4ff;
}

.batch-item.disabled {
    opacity: 0.6;
    background: #f5f5f5;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
}

.batch-id {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
}

.item-content .label {
    font-size: 26rpx;
    color: #666;
    margin-right: 10rpx;
}

.item-content .value {
    font-size: 26rpx;
    color: #333;
}

.item-status {
    text-align: center;
    margin-top: 15rpx;
    padding-top: 15rpx;
    border-top: 1rpx solid #e4e7ed;
}

.status-text {
    font-size: 24rpx;
    color: #909399;
}



.modal-content {
    padding: 20rpx 0;
}

.confirm-text {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
}

.batch-summary {
    max-height: 200rpx;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 15rpx;
    margin-bottom: 20rpx;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 8rpx 0;
    border-bottom: 1rpx solid #e4e7ed;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-id {
    font-size: 26rpx;
    font-weight: bold;
    color: #333;
}

.summary-product {
    font-size: 24rpx;
    color: #666;
}

.remark-section {
    margin-top: 20rpx;
}

.remark-label {
    font-size: 26rpx;
    color: #333;
    margin-bottom: 10rpx;
}
</style>
